Date: Mon, 14 Jul 2025 02:37:23 +0000 (UTC)
Message-ID: <964048853.273.1752460643637@550c9754c7f0>
Subject: Exported From Confluence
MIME-Version: 1.0
Content-Type: multipart/related; 
	boundary="----=_Part_272_1822263567.1752460643637"

------=_Part_272_1822263567.1752460643637
Content-Type: text/html; charset=UTF-8
Content-Transfer-Encoding: quoted-printable
Content-Location: file:///C:/exported.html

<html xmlns:o=3D'urn:schemas-microsoft-com:office:office'
      xmlns:w=3D'urn:schemas-microsoft-com:office:word'
      xmlns:v=3D'urn:schemas-microsoft-com:vml'
      xmlns=3D'urn:w3-org-ns:HTML'>
<head>
    <meta http-equiv=3D"Content-Type" content=3D"text/html; charset=3Dutf-8=
">
    <title>Multi-Tenant Authentication System - Implementation Plan</title>
    <!--[if gte mso 9]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:TargetScreenSize>1024x640</o:TargetScreenSize>
            <o:PixelsPerInch>72</o:PixelsPerInch>
            <o:AllowPNG/>
        </o:OfficeDocumentSettings>
        <w:WordDocument>
            <w:View>Print</w:View>
            <w:Zoom>90</w:Zoom>
            <w:DoNotOptimizeForBrowser/>
        </w:WordDocument>
    </xml>
    <![endif]-->
    <style>
                <!--
        @page Section1 {
            size: 8.5in 11.0in;
            margin: 1.0in;
            mso-header-margin: .5in;
            mso-footer-margin: .5in;
            mso-paper-source: 0;
        }

        table {
            border: solid 1px;
            border-collapse: collapse;
        }

        table td, table th {
            border: solid 1px;
            padding: 5px;
        }

        td {
            page-break-inside: avoid;
        }

        tr {
            page-break-after: avoid;
        }

        div.Section1 {
            page: Section1;
        }

        /* Confluence print stylesheet. Common to all themes for print medi=
a */
/* Full of !important until we improve batching for print CSS */

@media print {
    #main {
        padding-bottom: 1em !important; /* The default padding of 6em is to=
o much for printouts */
    }

    body {
        font: var(--ds-font-body-small, Arial, Helvetica, FreeSans, sans-se=
rif);
    }

    body, #full-height-container, #main, #page, #content, .has-personal-sid=
ebar #content {
        background: var(--ds-surface, #fff) !important;
        color: var(--ds-text, #000) !important;
        border: 0 !important;
        width: 100% !important;
        height: auto !important;
        min-height: auto !important;
        margin: 0 !important;
        padding: 0 !important;
        display: block !important;
    }

    a, a:link, a:visited, a:focus, a:hover, a:active {
        color: var(--ds-text, #000);
    }

    #content h1,
    #content h2,
    #content h3,
    #content h4,
    #content h5,
    #content h6 {
        page-break-after: avoid;
    }

    pre {
        font: var(--ds-font-code, Monaco, "Courier New", monospace);
    }

    #header,
    .aui-header-inner,
    #navigation,
    #sidebar,
    .sidebar,
    #personal-info-sidebar,
    .ia-fixed-sidebar,
    .page-actions,
    .navmenu,
    .ajs-menu-bar,
    .noprint,
    .inline-control-link,
    .inline-control-link a,
    a.show-labels-editor,
    .global-comment-actions,
    .comment-actions,
    .quick-comment-container,
    #addcomment {
        display: none !important;
    }

    /* CONF-28544 cannot print multiple pages in IE */
    #splitter-content {
        position: relative !important;
    }

    .comment .date::before {
        content: none !important; /* remove middot for print view */
    }

    h1.pagetitle img {
        height: auto;
        width: auto;
    }

    .print-only {
        display: block;
    }

    #footer {
        position: relative !important; /* CONF-17506 Place the footer at en=
d of the content */
        margin: 0;
        padding: 0;
        background: none;
        clear: both;
    }

    #poweredby {
        border-top: none;
        background: none;
    }

    #poweredby li.print-only {
        display: list-item;
        font-style: italic;
    }

    #poweredby li.noprint {
        display: none;
    }

    /* no width controls in print */
    .wiki-content .table-wrap,
    .wiki-content p,
    .panel .codeContent,
    .panel .codeContent pre,
    .image-wrap {
        overflow: visible !important;
    }

    /* TODO - should this work? */
    #children-section,
    #comments-section .comment,
    #comments-section .comment .comment-body,
    #comments-section .comment .comment-content,
    #comments-section .comment p {
        page-break-inside: avoid;
    }

    #page-children a {
        text-decoration: none;
    }

    /**
     hide twixies

     the specificity here is a hack because print styles
     are getting loaded before the base styles. */
    #comments-section.pageSection .section-header,
    #comments-section.pageSection .section-title,
    #children-section.pageSection .section-header,
    #children-section.pageSection .section-title,
    .children-show-hide {
        padding-left: 0;
        margin-left: 0;
    }

    .children-show-hide.icon {
        display: none;
    }

    /* personal sidebar */
    .has-personal-sidebar #content {
        margin-right: 0px;
    }

    .has-personal-sidebar #content .pageSection {
        margin-right: 0px;
    }

    .no-print, .no-print * {
        display: none !important;
    }
}
-->
    </style>
</head>
<body>
    <h1>Multi-Tenant Authentication System - Implementation Plan</h1>
    <div class=3D"Section1">
        <p>NOTE: This is a work in progress. The design is theoretical and =
may need to be adjusted when implemented. Please keep this document updated=
 with any necessary changes in order to keep this document relevant and to =
help your fellow developers.</p>
<hr>
<h2 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-Overview">Ove=
rview</h2>
<p>This document outlines the approach for implementing a new authenticatio=
n system for a multi-tenant SaaS platform. The system will support multiple=
 login methods per user, including:</p>
<ul>
<li>
<p>Username and password</p></li>
<li>
<p>OIDC providers (e.g., Google, Microsoft)</p></li>
</ul>
<p>The system is implemented using Go and PostgreSQL, and all identifiers w=
ill use UUIDs. New users will be created with UUIDv4, while migrated users =
will receive deterministic UUIDs derived from a constant namespace string a=
nd the original numeric ID.</p>
<h2 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-DatabaseSchem=
a">Database Schema</h2>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-User"><code>U=
ser</code></h3>
<p>This table holds core user identity information.</p>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: sql; gutter: false; theme: Confluence" data-theme=3D"Confluence">CREATE T=
ABLE User (
  Id UUID PRIMARY KEY,
  Orig_ID INT GENERATED BY DEFAULT AS IDENTITY,
  OrganizationId UUID NOT NULL,
  FirstName VARCHAR(45),
  LastName VARCHAR(45),
  Mobile VARCHAR(45),
  NotificationSmsEnabled BOOLEAN NOT NULL DEFAULT false,
  IANATimezone VARCHAR(45) NOT NULL DEFAULT 'America/Chicago',
  Description VARCHAR(255),
  LastLoginUTC TIMESTAMP,
  IsDeleted BOOLEAN NOT NULL DEFAULT false,
  DeletedAt TIMESTAMP,
  CONSTRAINT User_Organization FOREIGN KEY (OrganizationId)
    REFERENCES Organization(Id)
  CREATE INDEX Orig_ID_IDX ON Session(Orig_ID);
);
</pre>
</div>
</div>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-AuthMethod"><=
code>AuthMethod</code></h3>
<p>Represents a method of authentication associated with a user.</p>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: sql; gutter: false; theme: Confluence" data-theme=3D"Confluence">CREATE T=
ABLE AuthMethod (
  Id UUID PRIMARY KEY,
  UserId UUID NOT NULL,
  Type VARCHAR(32) NOT NULL CHECK (Type IN ('USERNAME_PASSWORD', 'OIDC')),
  Sub VARCHAR(255),
  Issuer VARCHAR(255),
  UserName VARCHAR(255),
  PasswordHash TEXT,
  Email VARCHAR(255),
  Metadata JSONB,
  LastLoginUTC TIMESTAMP,
  IsDeleted BOOLEAN NOT NULL DEFAULT false,
  DeletedAt TIMESTAMP,
  UNIQUE (Type, Issuer, Sub),
  FOREIGN KEY (UserId) REFERENCES User(Id)
);

CREATE UNIQUE INDEX AuthMethod_UserName_UQ ON AuthMethod(UserName) WHERE Ty=
pe =3D 'USERNAME_PASSWORD';
</pre>
</div>
</div>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-Session"><cod=
e>Session</code></h3>
<p>Tracks user sessions and enables secure logout and expiration.</p>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: sql; gutter: false; theme: Confluence" data-theme=3D"Confluence">CREATE T=
ABLE Session (
  Id UUID PRIMARY KEY,
  UserId UUID NOT NULL,
  AuthMethodId UUID NOT NULL,
  CreatedUTC TIMESTAMP NOT NULL,
  LastUsedUTC TIMESTAMP,
  ExpiresUTC TIMESTAMP NOT NULL,
  IPAddress VARCHAR(45),
  UserAgent TEXT,
  IsRevoked BOOLEAN NOT NULL DEFAULT false,
  FOREIGN KEY (UserId) REFERENCES User(Id),
  FOREIGN KEY (AuthMethodId) REFERENCES AuthMethod(Id)
);

CREATE INDEX Session_UserId_IDX ON Session(UserId);
CREATE INDEX Session_AuthMethodId_IDX ON Session(AuthMethodId);
</pre>
</div>
</div>
<p>A user may be logged in through multiple methods and may have multiple a=
ctive sessions concurrently.</p>
<hr>
<h2 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-UseCases">Use=
 Cases</h2>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-1.Username/Pa=
sswordLogin">1. Username/Password Login</h3>
<ul>
<li>
<p>Find <code>AuthMethod</code> of type <code>USERNAME_PASSWORD</code> by u=
sername</p></li>
<li>
<p>Verify password hash</p></li>
<li>
<p>Create new session in <code>Session</code> table</p></li>
</ul>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-2.GoogleOIDCL=
ogin">2. Google OIDC Login</h3>
<ul>
<li>
<p>Extract <code>sub</code>, <code>iss</code>, and email from token</p></li=
>
<li>
<p>Find or create <code>AuthMethod</code> with <code>Issuer =3D 'https://ac=
counts.google.com'</code></p></li>
<li>
<p>If <code>AuthMethod</code> not found:</p>
<ul>
<li>
<p>Create new <code>User</code></p></li>
<li>
<p>Create new <code>AuthMethod</code> for that user</p></li>
</ul></li>
<li>
<p>Create new session</p></li>
</ul>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-3.MicrosoftOI=
DCLogin">3. Microsoft OIDC Login</h3>
<ul>
<li>
<p>Similar to Google, but with <code>Issuer =3D 'https://login.microsoftonl=
ine.com/common/v2.0'</code></p></li>
</ul>
<hr>
<h2 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-CallbackHandl=
er(GoExample)">Callback Handler (Go Example)</h2>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-a.PopulatingS=
essionFromOIDCCallback">a. Populating Session From OIDC Callback</h3>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: java; gutter: false; theme: Confluence" data-theme=3D"Confluence">func ha=
ndleOIDCCallback(ctx context.Context, claims *oidc.IDToken, provider string=
, w http.ResponseWriter, r *http.Request) error {
    sub :=3D claims.Subject
    issuer :=3D claims.Issuer
    email :=3D claims.Email
    authMethod, err :=3D findOrCreateAuthMethod(ctx, issuer, sub, email)
    if err !=3D nil {
        return err
    }
    session, err :=3D createSession(ctx, authMethod.UserID, authMethod.ID)
    if err !=3D nil {
        return err
    }
    return setSessionCookie(w, r, session.ID)
}
</pre>
</div>
</div>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-HelperFunctio=
ns">Helper Functions</h3>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: java; gutter: false; theme: Confluence" data-theme=3D"Confluence">func ex=
tractSessionIDFromCookie(r *http.Request) (uuid.UUID, error) {
    cookie, err :=3D r.Cookie("session_id")
    if err !=3D nil {
        return uuid.Nil, err
    }
    return uuid.Parse(cookie.Value)
}

func setSessionCookie(w http.ResponseWriter, r *http.Request, sessionID uui=
d.UUID) error {
    // TODO: replace this with actual dev-mode check logic
    secure :=3D !(/* TODO: isDev check */ false || strings.HasPrefix(r.Host=
, "onramp:4200"))
    http.SetCookie(w, &amp;http.Cookie{
        Name:     "session_id",
        Value:    sessionID.String(),
        Path:     "/",
        HttpOnly: true,
        Secure:   secure,
        SameSite: http.SameSiteStrictMode,
    })
    return nil
}

func lookupSession(ctx context.Context, id uuid.UUID) (*Session, error) {
    // TODO: replace with actual database query logic
    const query =3D `
        SELECT Id, UserId, AuthMethodId, CreatedUTC, LastUsedUTC, ExpiresUT=
C, IPAddress, UserAgent, IsRevoked
        FROM Session
        WHERE Id =3D $1
    `
    // TODO: use database/sql or a query builder to execute and scan into a=
 Session struct
    return nil, nil
}

func logout(w http.ResponseWriter, r *http.Request) {
    sessionID, err :=3D extractSessionIDFromCookie(r)
    if err =3D=3D nil {
        // TODO: revoke session in database
        _, _ =3D db.ExecContext(r.Context(), `UPDATE Session SET IsRevoked =
=3D TRUE WHERE Id =3D $1`, sessionID)
    }
    http.SetCookie(w, &amp;http.Cookie{
        Name:     "session_id",
        Value:    "",
        Path:     "/",
        Expires:  time.Unix(0, 0),
        MaxAge:   -1,
        HttpOnly: true,
        Secure:   true, // TODO: same Secure handling as in setSessionCooki=
e
        SameSite: http.SameSiteStrictMode,
    })
    w.WriteHeader(http.StatusOK)
}
</pre>
</div>
</div>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-b.Recognizing=
aLogged-InUser">b. Recognizing a Logged-In User</h3>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: java; gutter: false; theme: Confluence" data-theme=3D"Confluence">session=
ID, err :=3D extractSessionIDFromCookie(r)
if err !=3D nil {
    http.Error(w, "Unauthorized", http.StatusUnauthorized)
    return
}
session, err :=3D lookupSession(r.Context(), sessionID)
if err !=3D nil || session.IsRevoked || time.Now().After(session.ExpiresUTC=
) {
    http.Error(w, "Unauthorized", http.StatusUnauthorized)
    return
}
</pre>
</div>
</div>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-SessionMiddle=
ware(Example)">Session Middleware (Example)</h3>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: java; gutter: false; theme: Confluence" data-theme=3D"Confluence">func Re=
quireSession(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        sessionID, err :=3D extractSessionIDFromCookie(r)
        if err !=3D nil {
            http.Error(w, "Unauthorized", http.StatusUnauthorized)
            return
        }
        session, err :=3D lookupSession(r.Context(), sessionID)
        if err !=3D nil || session.IsRevoked || time.Now().After(session.Ex=
piresUTC) {
            http.Error(w, "Unauthorized", http.StatusUnauthorized)
            return
        }
        ctx :=3D context.WithValue(r.Context(), "userID", session.UserID)
        next.ServeHTTP(w, r.WithContext(ctx))
    })
}
</pre>
</div>
</div>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-SessionVerifi=
cationExample">Session Verification Example</h3>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: java; gutter: false; theme: Confluence" data-theme=3D"Confluence">func ve=
rifySession(r *http.Request) (*Session, error) {
    sessionID, err :=3D extractSessionIDFromCookie(r)
    if err !=3D nil {
        return nil, fmt.Errorf("missing or invalid session: %w", err)
    }
    session, err :=3D lookupSession(r.Context(), sessionID)
    if err !=3D nil {
        return nil, fmt.Errorf("failed to retrieve session: %w", err)
    }
    if session.IsRevoked || time.Now().After(session.ExpiresUTC) {
        return nil, fmt.Errorf("session expired or revoked")
    }
    return session, nil
}
</pre>
</div>
</div>
<blockquote>
<p>Helper functions such as <code>findOrCreateAuthMethod</code>, <code>crea=
teSession</code>, and <code>setSessionCookie</code> should implement the lo=
gic described in the session management section.</p>
</blockquote>
<hr>
<h2 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-MigrationStra=
tegy">Migration Strategy</h2>
<p>To convert from the legacy <code>User</code> table using deterministic U=
UIDs:</p>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-Step1:RenameE=
xistingTable">Step 1: Rename Existing Table</h3>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: sql; gutter: false; theme: Confluence" data-theme=3D"Confluence">ALTER TA=
BLE User RENAME TO LegacyUser;
</pre>
</div>
</div>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-Step2:CreateN=
ewTables(User,AuthMethod,Session)fromSchemaSection">Step 2: Create New Tabl=
es (<code>User</code>, <code>AuthMethod</code>, <code>Session</code>) from =
Schema Section</h3>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-Step3:Createa=
ndPopulateNewUserTableUsingSQL-BasedDeterministicUUIDs">Step 3: Create and =
Populate New <code>User</code> Table Using SQL-Based Deterministic UUIDs</h=
3>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: sql; gutter: false; theme: Confluence" data-theme=3D"Confluence">INSERT I=
NTO User (
  Id,
  Orig_ID,
  OrganizationId,
  FirstName,
  LastName,
  Mobile,
  NotificationSmsEnabled,
  IANATimezone,
  Description,
  LastLoginUTC
)
SELECT
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_' || Id::text),
  Id,
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_org_' || OrganizationId::text),
  FirstName,
  LastName,
  Mobile,
  NotificationSmsEnabled,
  IANATimezone,
  Description,
  LastLoginUTC
FROM LegacyUser;
</pre>
</div>
</div>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-Step4:Createa=
ndPopulateNewAuthMethodTable">Step 4: Create and Populate New <code>AuthMet=
hod</code> Table</h3>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: sql; gutter: false; theme: Confluence" data-theme=3D"Confluence">INSERT I=
NTO AuthMethod (
  Id,
  UserId,
  Type,
  UserName,
  PasswordHash,
  Email,
  LastLoginUTC
)
SELECT
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_auth_' || Id::text),
  uuid_generate_v5(uuid_nil(), 'SYNAPSE_user_' || Id::text),
  'USERNAME_PASSWORD',
  UserName,
  Password,
  Email,
  LastLoginUTC
FROM LegacyUser;
</pre>
</div>
</div>
<blockquote>
<p>Note: Ensure the <code>uuid-ossp</code> extension is enabled:</p>
</blockquote>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: sql; gutter: false; theme: Confluence" data-theme=3D"Confluence">CREATE E=
XTENSION IF NOT EXISTS "uuid-ossp";
</pre>
</div>
</div>
<h3 id=3D"Multi-TenantAuthenticationSystem-ImplementationPlan-Step5:DropOld=
Table">Step 5: Drop Old Table</h3>
<div class=3D"code panel pdl" style=3D"border-width: 1px;">
<div class=3D"codeContent panelContent pdl">
<pre class=3D"syntaxhighlighter-pre" data-syntaxhighlighter-params=3D"brush=
: sql; gutter: false; theme: Confluence" data-theme=3D"Confluence">DROP TAB=
LE LegacyUser;
</pre>
</div>
</div>
<p>The remainder of the system (login, session tracking, etc.) operates on =
UUIDs natively.</p>
<p></p>
    </div>
</body>
</html>
------=_Part_272_1822263567.1752460643637--
