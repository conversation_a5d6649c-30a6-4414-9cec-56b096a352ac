# Roles Management API Implementation Plan

## Overview
This document outlines the detailed implementation plan for the roles management API endpoints under `/workspace/shared/rest/onramp/roles/`. The implementation follows the existing architectural patterns established in the codebase, particularly the organization and softwaregateway modules.

## Database Schema Analysis
Based on the schema.sql file, the roles system consists of:

### Core Tables:
- **`TemplateRole`**: Predefined role templates by organization type
- **`CustomRole`**: Organization-specific roles derived from templates
- **`Permission`**: Individual permissions scoped by organization type
- **`TemplateRolePermission`**: Default permissions for template roles
- **`CustomRolePermission`**: Actual permissions for custom roles
- **`Organization`**: Organizations that own custom roles
- **`OrgType`**: Organization types that determine available templates

### Key Relationships:
- CustomRole belongs to Organization (via OrganizationId)
- CustomRole is based on TemplateRole (via TemplateRoleIdentifier)
- Both template and custom roles have associated permissions
- Permissions are scoped by OrgType

## API Endpoints Implementation

### 1. GET /api/organization/{organizationId}/roles
**Purpose**: Retrieve all custom roles for a given organization

**Implementation Details**:
- **Handler**: `GetRolesHandler`
- **Database Query**: Join CustomRole with TemplateRole to get complete role information
- **Response**: Array of role objects with permissions
- **Error Handling**: 404 if organization not found, 500 for database errors

### 2. DELETE /api/organization/{organizationId}/roles/{roleId}
**Purpose**: Delete a custom role from the organization

**Implementation Details**:
- **Handler**: `DeleteRoleHandler`
- **Method**: Soft delete (set IsDeleted = true)
- **Validation**: Ensure role belongs to the organization
- **Response**: 204 No Content on success
- **Error Handling**: 404 if role/organization not found, 403 if role doesn't belong to org

### 3. POST /api/organization/{organizationId}/roles
**Purpose**: Create a new custom role for the organization

**Implementation Details**:
- **Handler**: `CreateRoleHandler`
- **Request Body**: JSON with name, description, templateRoleIdentifier, permissions
- **Validation**: Template role must be valid for organization type
- **Database Operations**: 
  1. Insert into CustomRole
  2. Insert permissions into CustomRolePermission
- **Response**: Created role object with generated UUID

### 4. GET /api/organization/{organizationId}/role-templates
**Purpose**: Retrieve available role templates for the organization type

**Implementation Details**:
- **Handler**: `GetRoleTemplatesHandler`
- **Database Query**: Join Organization -> OrgType -> TemplateRole -> TemplateRolePermission
- **Response**: Array of template roles with default permissions
- **Caching**: Consider caching since templates change infrequently

## File Structure

```
/workspace/shared/rest/onramp/roles/
├── roles.go              # Main handlers and business logic
├── schemas.go            # Request/response structures
├── errors.go             # Custom error definitions
└── templates.go          # Role template specific handlers
```

## Data Models

### Request Structures:
```go
type CreateRoleRequest struct {
    Name                   string                    `json:"name" validate:"required,min=1"`
    Description           string                    `json:"description" validate:"required,min=1"`
    TemplateRoleIdentifier string                    `json:"templateRoleIdentifier" validate:"required"`
    Permissions           []CustomPermissionRequest `json:"permissions"`
}

type CustomPermissionRequest struct {
    PermissionIdentifier string `json:"permissionIdentifier" validate:"required"`
    Value               bool   `json:"value"`
}
```

### Response Structures:
```go
type CustomRoleResponse struct {
    Id                     string                     `json:"id"`
    Name                   string                     `json:"name"`
    Description           string                     `json:"description"`
    TemplateRoleIdentifier string                     `json:"templateRoleIdentifier"`
    TemplateName          string                     `json:"templateName"`
    Permissions           []CustomPermissionResponse `json:"permissions"`
    CreatedAt             time.Time                  `json:"createdAt"`
    UpdatedAt             time.Time                  `json:"updatedAt"`
}

type TemplateRoleResponse struct {
    Identifier          string                        `json:"identifier"`
    Name               string                        `json:"name"`
    Description        string                        `json:"description"`
    DefaultPermissions []TemplatePermissionResponse  `json:"defaultPermissions"`
}
```

## Database Queries

### Get Organization Roles:
```sql
SELECT 
    cr.Id,
    cr.Name,
    cr.Description,
    cr.TemplateRoleIdentifier,
    tr.Name as TemplateName,
    cr.CreatedAt,
    cr.UpdatedAt,
    crp.PermissionIdentifier,
    crp.Value,
    p.Name as PermissionName,
    p.Description as PermissionDescription
FROM {{CustomRole}} cr
INNER JOIN {{TemplateRole}} tr ON cr.TemplateRoleIdentifier = tr.Identifier
LEFT JOIN {{CustomRolePermission}} crp ON cr.Id = crp.CustomRoleId AND crp.IsDeleted = false
LEFT JOIN {{Permission}} p ON crp.PermissionIdentifier = p.Identifier
WHERE cr.OrganizationId = $1 AND cr.IsDeleted = false
ORDER BY cr.CreatedAt DESC, crp.PermissionIdentifier
```

### Get Role Templates by Organization:
```sql
SELECT 
    tr.Identifier,
    tr.Name,
    tr.Description,
    trp.PermissionIdentifier,
    trp.DefaultValue,
    p.Name as PermissionName,
    p.Description as PermissionDescription
FROM {{Organization}} o
INNER JOIN {{TemplateRole}} tr ON o.OrgTypeIdentifier = tr.OrgTypeIdentifier
LEFT JOIN {{TemplateRolePermission}} trp ON tr.Identifier = trp.TemplateRoleIdentifier AND trp.IsDeleted = false
LEFT JOIN {{Permission}} p ON trp.PermissionIdentifier = p.Identifier
WHERE o.Id = $1 AND o.IsDeleted = false
ORDER BY tr.Name, trp.PermissionIdentifier
```

## Error Handling Strategy

Following the established patterns:
- **Custom Errors**: Define specific errors for each failure case
- **HTTP Status Codes**: Use appropriate status codes (400, 404, 500)
- **Logging**: Log errors with context for debugging
- **Response Format**: Use standardized response structure

## Security Considerations

1. **Authorization**: Validate user has permission to manage roles for the organization
2. **Input Validation**: Sanitize and validate all input parameters
3. **SQL Injection**: Use parameterized queries
4. **Rate Limiting**: Consider implementing rate limiting for role creation

## Testing Strategy

1. **Unit Tests**: Test individual functions with mocked dependencies
2. **Integration Tests**: Test database operations with test database
3. **API Tests**: Test complete HTTP request/response cycle
4. **Error Cases**: Test all error scenarios and edge cases

## Performance Considerations

1. **Database Indexing**: Ensure proper indexes on OrganizationId, TemplateRoleIdentifier
2. **Query Optimization**: Use efficient joins and avoid N+1 queries
3. **Caching**: Cache role templates since they change infrequently
4. **Pagination**: Consider pagination for organizations with many roles

## Migration Considerations

- No schema changes required - using existing tables
- Ensure proper foreign key constraints are in place
- Consider data seeding for initial template roles and permissions

## Next Steps

1. Implement the basic file structure and schemas
2. Create the main handlers following the established patterns
3. Implement database operations with proper error handling
4. Add comprehensive tests
5. Integration with existing middleware and routing
6. Documentation and API specification updates
