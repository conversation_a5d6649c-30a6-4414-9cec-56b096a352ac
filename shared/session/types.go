package session

import (
	"time"

	"golang.org/x/oauth2"
	"synapse-its.com/shared/api/authorizer"
)

// SessionData represents all session information stored server-side
type SessionData struct {
	OAuthToken      *oauth2.Token               `json:"oauth_token"`
	UserID          string                      `json:"user_id"`
	UserPermissions *authorizer.UserPermissions `json:"user_permissions"`
	CreatedAt       time.Time                   `json:"created_at"`
	LastAccessedAt  time.Time                   `json:"last_accessed_at"`
	ExpiresAt       time.Time                   `json:"expires_at"`
}

// IsExpired checks if the session has expired
func (s *SessionData) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// Touch updates the last accessed time
func (s *SessionData) Touch() {
	s.LastAccessedAt = time.Now()
}

// Context keys for session data
type contextKey string

const (
	SessionDataKey     contextKey = "session_data"
	UserPermissionsKey contextKey = "user_permissions"
	UserClaimsKey      contextKey = "user_claims"
)
