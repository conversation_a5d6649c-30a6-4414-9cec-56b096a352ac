# In-Memory Session Tracking Implementation Plan

## Overview

This document provides a detailed step-by-step implementation plan for creating an in-memory session tracking system that will replace the current simple session store in `microservices/onramp/routes.go`. The implementation will provide a centralized API for session management that can later be migrated to Redis.

## Current State Analysis

### Existing Implementation
- **Location**: `microservices/onramp/routes.go`
- **Current Store**: `var sessionStore = map[string]*oauth2.Token{}`
- **Session ID**: Generated using `util.RandomString(32)`
- **Cookie Name**: `session_id`
- **Context Key**: `userKey` (type `key int`, value `0`)

### Current Session Flow
1. User logs in via OIDC → `handleLogin()`
2. OAuth callback → `handleCallback()` → creates session with `oauth2.Token`
3. Subsequent requests → `authMiddleware()` → validates session and adds claims to context
4. Logout → `handleLogout()` → deletes session

## Target Architecture

### Session Data Structure
```go
type SessionData struct {
    OAuthToken      *oauth2.Token                    `json:"oauth_token"`
    UserID          string                           `json:"user_id"`
    UserPermissions *authorizer.UserPermissions      `json:"user_permissions"`
    CreatedAt       time.Time                        `json:"created_at"`
    LastAccessedAt  time.Time                        `json:"last_accessed_at"`
    ExpiresAt       time.Time                        `json:"expires_at"`
}
```

### Session Store Interface
```go
type SessionStore interface {
    Get(sessionID string) (*SessionData, error)
    Set(sessionID string, data *SessionData) error
    Delete(sessionID string) error
    Clear() error
    Cleanup() error // Remove expired sessions
}
```

## Implementation Steps

### Step 1: Create Session Package Structure
**Location**: `shared/session/`

Create the following files:
- `shared/session/types.go` - Session data structures
- `shared/session/store.go` - Session store interface and in-memory implementation
- `shared/session/middleware.go` - Session middleware
- `shared/session/store_test.go` - Unit tests for store
- `shared/session/middleware_test.go` - Unit tests for middleware

### Step 2: Implement Session Data Types
**File**: `shared/session/types.go`

```go
package session

import (
    "time"
    "golang.org/x/oauth2"
    "synapse-its.com/shared/api/authorizer"
)

// SessionData represents all session information stored server-side
type SessionData struct {
    OAuthToken      *oauth2.Token                    `json:"oauth_token"`
    UserID          string                           `json:"user_id"`
    UserPermissions *authorizer.UserPermissions      `json:"user_permissions"`
    CreatedAt       time.Time                        `json:"created_at"`
    LastAccessedAt  time.Time                        `json:"last_accessed_at"`
    ExpiresAt       time.Time                        `json:"expires_at"`
}

// IsExpired checks if the session has expired
func (s *SessionData) IsExpired() bool {
    return time.Now().After(s.ExpiresAt)
}

// Touch updates the last accessed time
func (s *SessionData) Touch() {
    s.LastAccessedAt = time.Now()
}

// Context keys for session data
type contextKey string

const (
    SessionDataKey    contextKey = "session_data"
    UserPermissionsKey contextKey = "user_permissions"
    UserClaimsKey     contextKey = "user_claims"
)
```

### Step 3: Implement Session Store Interface
**File**: `shared/session/store.go`

```go
package session

import (
    "errors"
    "sync"
    "time"
)

var (
    ErrSessionNotFound = errors.New("session not found")
    ErrSessionExpired  = errors.New("session expired")
)

// SessionStore defines the interface for session storage
type SessionStore interface {
    Get(sessionID string) (*SessionData, error)
    Set(sessionID string, data *SessionData) error
    Delete(sessionID string) error
    Clear() error
    Cleanup() error
}

// InMemorySessionStore implements SessionStore using in-memory storage
type InMemorySessionStore struct {
    sessions map[string]*SessionData
    mutex    sync.RWMutex
    ttl      time.Duration
}

// NewInMemorySessionStore creates a new in-memory session store
func NewInMemorySessionStore(ttl time.Duration) *InMemorySessionStore {
    store := &InMemorySessionStore{
        sessions: make(map[string]*SessionData),
        ttl:      ttl,
    }
    
    // Start cleanup goroutine
    go store.startCleanupRoutine()
    
    return store
}

// Get retrieves session data by session ID
func (s *InMemorySessionStore) Get(sessionID string) (*SessionData, error) {
    s.mutex.RLock()
    defer s.mutex.RUnlock()
    
    data, exists := s.sessions[sessionID]
    if !exists {
        return nil, ErrSessionNotFound
    }
    
    if data.IsExpired() {
        return nil, ErrSessionExpired
    }
    
    // Update last accessed time
    data.Touch()
    
    return data, nil
}

// Set stores session data
func (s *InMemorySessionStore) Set(sessionID string, data *SessionData) error {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    // Set expiration if not already set
    if data.ExpiresAt.IsZero() {
        data.ExpiresAt = time.Now().Add(s.ttl)
    }
    
    s.sessions[sessionID] = data
    return nil
}

// Delete removes a session
func (s *InMemorySessionStore) Delete(sessionID string) error {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    delete(s.sessions, sessionID)
    return nil
}

// Clear removes all sessions
func (s *InMemorySessionStore) Clear() error {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    s.sessions = make(map[string]*SessionData)
    return nil
}

// Cleanup removes expired sessions
func (s *InMemorySessionStore) Cleanup() error {
    s.mutex.Lock()
    defer s.mutex.Unlock()
    
    now := time.Now()
    for sessionID, data := range s.sessions {
        if now.After(data.ExpiresAt) {
            delete(s.sessions, sessionID)
        }
    }
    
    return nil
}

// startCleanupRoutine runs periodic cleanup of expired sessions
func (s *InMemorySessionStore) startCleanupRoutine() {
    ticker := time.NewTicker(time.Hour) // Cleanup every hour
    defer ticker.Stop()
    
    for range ticker.C {
        s.Cleanup()
    }
}
```

### Step 4: Implement Session Middleware
**File**: `shared/session/middleware.go`

```go
package session

import (
    "context"
    "net/http"
    "strings"
    "time"
    
    "github.com/coreos/go-oidc"
    "golang.org/x/oauth2"
    "synapse-its.com/shared/api/authorizer"
    "synapse-its.com/shared/connect"
    "synapse-its.com/shared/logger"
)

// SessionMiddleware handles session validation and context injection
type SessionMiddleware struct {
    store        SessionStore
    cookieName   string
    oidcVerifier *oidc.IDTokenVerifier
}

// NewSessionMiddleware creates a new session middleware
func NewSessionMiddleware(store SessionStore, cookieName string, verifier *oidc.IDTokenVerifier) *SessionMiddleware {
    return &SessionMiddleware{
        store:        store,
        cookieName:   cookieName,
        oidcVerifier: verifier,
    }
}

// Middleware returns the HTTP middleware function
func (sm *SessionMiddleware) Middleware(next http.Handler) http.Handler {
    return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
        ctx := r.Context()
        
        // Extract session ID from cookie
        cookie, err := r.Cookie(sm.cookieName)
        if err != nil {
            http.Error(w, "unauthorized: no session cookie", http.StatusUnauthorized)
            return
        }
        
        // Get session data
        sessionData, err := sm.store.Get(cookie.Value)
        if err != nil {
            if err == ErrSessionNotFound || err == ErrSessionExpired {
                http.Error(w, "unauthorized: invalid session", http.StatusUnauthorized)
                return
            }
            logger.Errorf("Session store error: %v", err)
            http.Error(w, "internal server error", http.StatusInternalServerError)
            return
        }
        
        // Verify ID token (optional, can be disabled for performance)
        claims, err := sm.verifyIDToken(ctx, sessionData.OAuthToken)
        if err != nil {
            logger.Errorf("ID token verification failed: %v", err)
            http.Error(w, "unauthorized: invalid token", http.StatusUnauthorized)
            return
        }
        
        // Add session data to context
        ctx = context.WithValue(ctx, SessionDataKey, sessionData)
        ctx = context.WithValue(ctx, UserPermissionsKey, sessionData.UserPermissions)
        ctx = context.WithValue(ctx, UserClaimsKey, claims)
        
        // Continue to next handler
        next.ServeHTTP(w, r.WithContext(ctx))
    })
}

// verifyIDToken verifies the OIDC ID token
func (sm *SessionMiddleware) verifyIDToken(ctx context.Context, token *oauth2.Token) (map[string]interface{}, error) {
    rawID, ok := token.Extra("id_token").(string)
    if !ok {
        return nil, errors.New("no id_token in oauth token")
    }
    
    idToken, err := sm.oidcVerifier.Verify(ctx, rawID)
    if err != nil {
        return nil, err
    }
    
    var claims map[string]interface{}
    if err := idToken.Claims(&claims); err != nil {
        return nil, err
    }
    
    return claims, nil
}

// CreateSession creates a new session with the provided data
func CreateSession(store SessionStore, sessionID string, token *oauth2.Token, userID string, userPermissions *authorizer.UserPermissions) error {
    sessionData := &SessionData{
        OAuthToken:      token,
        UserID:          userID,
        UserPermissions: userPermissions,
        CreatedAt:       time.Now(),
        LastAccessedAt:  time.Now(),
        ExpiresAt:       time.Now().Add(24 * time.Hour), // 24 hour default
    }
    
    return store.Set(sessionID, sessionData)
}

// Helper functions for extracting data from context

// GetSessionData extracts session data from context
func GetSessionData(ctx context.Context) (*SessionData, bool) {
    data, ok := ctx.Value(SessionDataKey).(*SessionData)
    return data, ok
}

// GetUserPermissions extracts user permissions from context
func GetUserPermissions(ctx context.Context) (*authorizer.UserPermissions, bool) {
    perms, ok := ctx.Value(UserPermissionsKey).(*authorizer.UserPermissions)
    return perms, ok
}

// GetUserClaims extracts user claims from context
func GetUserClaims(ctx context.Context) (map[string]interface{}, bool) {
    claims, ok := ctx.Value(UserClaimsKey).(map[string]interface{})
    return claims, ok
}
```

### Step 5: Create Unit Tests
**File**: `shared/session/store_test.go`

Create comprehensive unit tests covering:
- Session creation, retrieval, deletion
- Session expiration handling
- Concurrent access safety
- Cleanup functionality

**File**: `shared/session/middleware_test.go`

Create tests covering:
- Middleware authentication flow
- Context injection
- Error handling scenarios
- Cookie validation

### Step 6: Update Onramp Routes
**File**: `microservices/onramp/routes.go`

1. Replace the global `sessionStore` variable
2. Update `handleCallback()` to use new session creation
3. Update `handleLogout()` to use new session deletion
4. Replace `authMiddleware()` with new session middleware
5. Update context key usage throughout

### Step 7: Integration Steps

1. **Add session package dependency** to `microservices/onramp/go.mod`
2. **Initialize session store** in main application startup
3. **Replace middleware** in router configuration
4. **Update handlers** to use new context extraction methods
5. **Update tests** to work with new session system

### Step 8: Migration Considerations

1. **Backward Compatibility**: Ensure existing sessions continue to work during deployment
2. **Configuration**: Add session TTL and cleanup interval configuration
3. **Monitoring**: Add metrics for session creation, validation, and cleanup
4. **Documentation**: Update API documentation for new context structure

## Configuration

### Environment Variables
```
SESSION_TTL=24h                    # Session time-to-live
SESSION_CLEANUP_INTERVAL=1h        # Cleanup interval for expired sessions
SESSION_COOKIE_NAME=session_id     # Cookie name for session ID
```

### Default Values
- Session TTL: 24 hours
- Cleanup interval: 1 hour
- Cookie name: "session_id"
- Cookie settings: HttpOnly, Secure (in production), SameSite=Strict

## Future Migration to Redis

The interface-based design allows easy migration to Redis:

1. Implement `RedisSessionStore` that satisfies `SessionStore` interface
2. Replace `NewInMemorySessionStore()` with `NewRedisSessionStore()`
3. No changes needed to middleware or application code

## Testing Strategy

1. **Unit Tests**: Test each component in isolation
2. **Integration Tests**: Test complete authentication flow
3. **Load Tests**: Verify performance with concurrent sessions
4. **Security Tests**: Validate session security and expiration

## Security Considerations

1. **Session ID Generation**: Use cryptographically secure random strings
2. **Cookie Security**: HttpOnly, Secure, SameSite attributes
3. **Session Expiration**: Automatic cleanup of expired sessions
4. **Token Validation**: Optional ID token re-verification
5. **Context Isolation**: Proper context key typing to prevent conflicts

## Performance Considerations

1. **Memory Usage**: Monitor session store memory consumption
2. **Cleanup Efficiency**: Periodic cleanup of expired sessions
3. **Concurrent Access**: Thread-safe operations with minimal locking
4. **Token Verification**: Optional caching of verified tokens

This implementation provides a robust, testable, and maintainable session management system that can be easily migrated to Redis in the future while maintaining the same API interface.
